/*! angular-ladda 0.2.2 */
!function(a,e){"use strict";var t;if("object"==typeof exports){try{t=require("ladda")}catch(n){}module.exports=e(t)}else"function"==typeof define&&define.amd?define(function(a){var n="ladda";try{t=a(n)}catch(d){}return e(t)}):a.<PERSON>dda=e(a.<PERSON>dd<PERSON>)}(this,function(a){"use strict";angular.module("angular-ladda",[]).provider("ladda",function(){var a={style:"zoom-in"};return{setOption:function(e){angular.extend(a,e)},$get:function(){return a}}}).directive("ladda",["ladda",function(e){return{restrict:"A",priority:-1,link:function(t,n,d){if(n.addClass("ladda-button"),angular.isUndefined(n.attr("data-style"))&&n.attr("data-style",e.style||"zoom-in"),!n[0].querySelector(".ladda-label")){var r=document.createElement("span");r.className="ladda-label",angular.element(r).append(n.contents()),n.append(r)}var i=a.create(n[0]);t.$watch(d.ladda,function(a){a||angular.isNumber(a)?(i.isLoading()||i.start(),angular.isNumber(a)&&i.setProgress(a)):(i.stop(),d.ngDisabled&&n.attr("disabled",t.$eval(d.ngDisabled)))})}}}])});