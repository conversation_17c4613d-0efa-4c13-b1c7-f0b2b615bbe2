<!DOCTYPE html>
<!--
Copyright 2017 The SwitchyOmega Authors. Please see the AUTHORS file
for details.

This file is part of SwitchyOmega.

SwitchyOmega is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

SwitchyOmega is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with SwitchyOmega.  If not, see <http://www.gnu.org/licenses/>.
-->
<html lang="en" ng-app="omegaPopup" ng-controller="PopupCtrl" ng-csp>
  <head>
    <meta charset="utf-8">
    <title>{{'popup_title' | tr}}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="lib/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/popup.css">
  </head>
  <body ng-class="{&quot;with-condition-form&quot;: showConditionForm}">
    <ul ng-hide="showConditionForm || proxyNotControllable || showRequestInfo" class="popup-menu-nav nav nav-pills nav-stacked">
      <li ng-repeat="profile in builtinProfiles" ng-class="{active: isActive(profile.name), &quot;bg-info&quot;: isEffective(profile.name)}" class="profile"><a href="#" role="button" ng-attr-tabindex="{{100 + $index}}" ng-click="applyProfile(profile)" title="{{getProfileTitle(profile)}}" data-shortcut="+{{profile.name}}"><span omega-profile-inline="profile" icon="getIcon(profile)" options="availableProfiles" disp-name="dispNameFilter"></span></a></li>
      <li ng-show="!requestInfoProvided &amp;&amp; !!externalProfile" ng-class="{active: isActive(&quot;&quot;), &quot;bg-info&quot;: isEffective(&quot;&quot;)}" class="profile external-profile"><a href="#" role="button" ng-click="nameExternal.open = true" title="{{getProfileTitle(externalProfile)}}" data-shortcut="external">
          <form name="nameExternalForm" ng-submit="nameExternalForm.$valid &amp;&amp; saveExternal()"><span omega-profile-icon="externalProfile" icon="getIcon(externalProfile, &quot;normal&quot;)" options="availableProfiles" disp-name="dispNameFilter"></span> <span ng-show="!nameExternal.open">{{'popup_externalProfile' | tr}}</span>
            <input ng-show="!!nameExternal.open" ng-model="externalProfile.name" ng-blur="nameExternalForm.$valid &amp;&amp; saveExternal()" placeholder="{{&quot;popup_externalProfileName&quot; | tr}}" ui-validate="validateProfileName" autofocus class="form-control">
          </form></a></li>
      <li ng-show="!!requestInfoProvided" class="request-info bg-warning"><a href="#" role="button" ng-click="showRequestInfo = true" data-shortcut="requestInfo"><span class="glyphicon glyphicon-warning-sign text-warning"></span> {{'popup_requestErrorCount' | tr:[requestInfo.errorCount]}}</a></li>
      <li class="divider"></li>
      <li ng-repeat="profile in customProfiles" ng-class="{active: isActive(profile.name), &quot;bg-info&quot;: isEffective(profile.name)}" dropdown class="profile custom-profile"><a href="#" role="button" ng-click="applyProfile(profile)" ng-if="!profile.validResultProfiles" title="{{getProfileTitle(profile)}}"><span omega-profile-inline="profile" icon="getIcon(profile)" options="availableProfiles" disp-name="dispNameFilter"></span></a><a href="#" role="button" ng-click="applyProfile(profile)" ng-if="!!profile.validResultProfiles" title="{{getProfileTitle(profile)}}" class="profile-with-default-edit"><span omega-profile-inline="profile" icon="getIcon(profile)" options="availableProfiles" disp-name="dispNameFilter"></span> [{{profile.defaultProfileName}}]
          <button role="button" dropdown-toggle href="#" ng-click="$event.stopPropagation()" class="dropdown-toggle btn btn-default"><span class="glyphicon glyphicon-chevron-down"></span></button></a>
        <ul ng-if="!!profile.validResultProfiles" class="dropdown-menu">
          <li ng-repeat="p in profile.validResultProfiles" ng-class="{active: p.name == profile.defaultProfileName}"><a href="#" role="button" ng-click="setDefaultProfile(profile.name, p.name)" title="{{getProfileTitle(profile)}}"><span omega-profile-inline="p" options="availableProfiles" disp-name="dispNameFilter"></span></a></li>
        </ul>
      </li>
      <li ng-show="!!currentDomain &amp;&amp; validResultProfiles.length" class="divider"></li>
      <li ng-show="!!currentProfileCanAddRule &amp;&amp; !!currentDomain"><a href="#" role="button" ng-click="prepareConditionForm()" data-shortcut="addRule"><span class="glyphicon glyphicon-plus"></span> <span>{{'popup_addCondition' | tr}}</span></a></li>
      <li ng-show="!!currentDomain &amp;&amp; validResultProfiles.length" dropdown is-open="tempRuleMenu.open"><a href="#" role="button" dropdown-toggle data-shortcut="tempRule" class="dropdown-toggle"><span class="glyphicon glyphicon-filter"></span> <span class="current-domain">{{currentDomain}}</span><span class="caret"></span></a>
        <ul class="dropdown-menu">
          <li ng-repeat="profile in validResultProfiles" ng-class="{active: profile.name == currentTempRuleProfile}" ng-show="!!currentTempRuleProfile || validResultProfiles.length == 1 || profile.name != currentProfileName"><a href="#" role="button" ng-click="addTempRule(currentDomain, profile.name)" title="{{getProfileTitle(profile)}}"><span omega-profile-inline="profile" options="availableProfiles" disp-name="dispNameFilter"></span></a></li>
        </ul>
      </li>
      <li class="divider"></li>
      <li><a href="#" role="button" ng-click="openOptions()" data-shortcut="option"><span class="glyphicon glyphicon-wrench"></span> <span>{{'popup_showOptions' | tr}}</span></a></li>
    </ul>
    <form name="conditionForm" style="display: none;" ng-style="{display: showConditionForm ? &quot;block&quot; : &quot;none&quot;}" ng-submit="addCondition(rule.condition, rule.profileName)" class="condition-form">
      <fieldset>
        <legend>{{'popup_addConditionTo' | tr}} <span class="profile-inline"><span omega-profile-inline="currentProfile" options="availableProfiles" disp-name="dispNameFilter"></span></span>
        </legend>
        <div class="form-group">
          <label>{{'options_conditionType' | tr}} 
            <button type="button" ng-click="openConditionHelp()" class="btn btn-link btn-sm clear-padding">{{"options_showConditionTypeHelp" | tr}} <span class="glyphicon glyphicon-new-window"></span>
            </button>
          </label>
          <select ng-model="rule.condition.conditionType" class="form-control">
            <option value="HostWildcardCondition">{{'condition_HostWildcardCondition' | tr}}</option>
            <option value="HostRegexCondition">{{'condition_HostRegexCondition' | tr}}</option>
            <option value="UrlWildcardCondition">{{'condition_UrlWildcardCondition' | tr}}</option>
            <option value="UrlRegexCondition">{{'condition_UrlRegexCondition' | tr}}</option>
            <option value="KeywordCondition">{{'condition_KeywordCondition' | tr}}</option>
          </select>
        </div>
        <div class="form-group">
          <label>{{'options_conditionDetails' | tr}}</label>
          <input type="text" required ng-model="rule.condition.pattern" autofocus class="form-control condition-details">
        </div>
        <div class="form-group">
          <label>{{'options_resultProfile' | tr}}</label>
          <div omega-profile-select="validResultProfiles" ng-model="rule.profileName" disp-name="dispNameFilter" options="availableProfiles"></div>
        </div>
        <div class="condition-controls">
          <button type="button" ng-click="returnToMenu()" class="btn btn-default">{{'dialog_cancel' | tr}}</button>
          <button type="submit" ng-disabled="conditionForm.$invalid" class="btn btn-primary">{{'popup_addCondition' | tr}}</button>
        </div>
      </fieldset>
    </form>
    <div ng-show="proxyNotControllable" class="proxy-not-controllable">
      <p class="text-danger">{{'popup_proxyNotControllable_' + proxyNotControllable | tr}}</p>
      <p class="help-block">{{('popup_proxyNotControllableDetails_' + proxyNotControllable | tr) || ('popup_proxyNotControllableDetails' | tr)}}</p>
      <p class="proxy-not-controllable-controls">
        <button ng-click="closePopup()" class="btn btn-default">{{'dialog_cancel' | tr}}</button>
        <button ng-click="openManage()" class="btn btn-primary">{{'popup_proxyNotControllableManage' | tr}}</button>
      </p>
    </div>
    <div ng-show="showRequestInfo" class="request-info-details"></div>
    <form style="display: none;" ng-style="{display: showRequestInfo ? &quot;block&quot; : &quot;none&quot;}" ng-submit="addConditionForDomains(domainsForCondition, profileForDomains)" class="request-info-details">
      <fieldset>
        <legend ng-show="!!currentProfileCanAddRule">{{'popup_addConditionTo' | tr}} <span class="profile-inline"><span omega-profile-inline="currentProfile" options="availableProfiles" disp-name="dispNameFilter"></span></span>
        </legend>
        <legend ng-show="!currentProfileCanAddRule">{{'popup_requestErrorHeading' | tr}} </legend>
        <div class="text-warning">{{'popup_requestErrorWarning' | tr}}</div>
        <p class="help-block">{{'popup_requestErrorWarningHelp' | tr}}</p>
        <p ng-show="!!currentProfileCanAddRule" class="help-block">{{'popup_requestErrorAddCondition' | tr}} </p>
        <div ng-repeat="domain in requestInfo.domains track by domain.domain" class="checkbox">
          <label>
            <input type="checkbox" ng-model="domainsForCondition[domain.domain]" autofocus ng-if="$index === 0">
            <input type="checkbox" ng-model="domainsForCondition[domain.domain]" ng-if="$index &gt; 0"><span class="label label-warning">{{domain.errorCount}}</span> {{domain.domain}}
          </label>
        </div>
        <div ng-show="!!currentProfileCanAddRule" class="form-group">
          <label>{{'options_resultProfileForSelectedDomains' | tr}}</label>
          <div omega-profile-select="validResultProfiles" ng-model="profileForDomains" disp-name="dispNameFilter" options="availableProfiles"></div>
        </div>
        <p ng-show="!currentProfileCanAddRule" class="help-block">{{'popup_requestErrorCannotAddCondition' | tr}}</p>
        <div class="condition-controls">
          <button type="button" ng-click="returnToMenu()" class="btn btn-default">{{'dialog_cancel' | tr}}</button>
          <button type="submit" ng-show="!!currentProfileCanAddRule" class="btn btn-primary">{{'popup_addCondition' | tr}}</button>
          <button type="button" ng-show="!currentProfileCanAddRule" ng-click="openOptions(&quot;#/general&quot;)" class="btn btn-default pull-right">{{'popup_configureMonitorWebRequests' | tr}}</button>
        </div>
      </fieldset>
    </form>
    <script src="js/log_error.js"></script>
    <script src="lib/jquery/jquery.min.js"></script>
    <script src="lib/angular/angular.min.js"></script>
    <script src="lib/angular-bootstrap/ui-bootstrap-tpls.min.js"></script>
    <script src="lib/angular-ui-utils/validate.min.js"></script>
    <script src="js/omega_target_web.js"></script>
    <script src="js/omega_decoration.js"></script>
    <script src="js/popup.js"></script>
  </body>
</html>