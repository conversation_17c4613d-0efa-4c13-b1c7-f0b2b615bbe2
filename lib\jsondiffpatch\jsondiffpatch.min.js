!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var f;"undefined"!=typeof window?f=window:"undefined"!=typeof global?f=global:"undefined"!=typeof self&&(f=self),f.jsondiffpatch=e()}}(function(){var define,module,exports;return (function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require=="function"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error("Cannot find module '"+o+"'");throw f.code="MODULE_NOT_FOUND",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){
var environment=require("./environment"),DiffPatcher=require("./diffpatcher").DiffPatcher;exports.DiffPatcher=DiffPatcher,exports.create=function(e){return new DiffPatcher(e)},exports.dateReviver=require("./date-reviver");var defaultInstance;if(exports.diff=function(){return defaultInstance||(defaultInstance=new DiffPatcher),defaultInstance.diff.apply(defaultInstance,arguments)},exports.patch=function(){return defaultInstance||(defaultInstance=new DiffPatcher),defaultInstance.patch.apply(defaultInstance,arguments)},exports.unpatch=function(){return defaultInstance||(defaultInstance=new DiffPatcher),defaultInstance.unpatch.apply(defaultInstance,arguments)},exports.reverse=function(){return defaultInstance||(defaultInstance=new DiffPatcher),defaultInstance.reverse.apply(defaultInstance,arguments)},environment.isBrowser)exports.homepage="https://github.com/benjamine/jsondiffpatch",exports.version="0.1.42";else{var packageInfoModuleName="../package.json",packageInfo=require(packageInfoModuleName);exports.homepage=packageInfo.homepage,exports.version=packageInfo.version;var formatterModuleName="./formatters",formatters=require(formatterModuleName);exports.formatters=formatters,exports.console=formatters.console}
},{"./date-reviver":6,"./diffpatcher":7,"./environment":8}],2:[function(require,module,exports){
var Pipe=require("../pipe").Pipe,Context=function(){};Context.prototype.setResult=function(t){return this.result=t,this.hasResult=!0,this},Context.prototype.exit=function(){return this.exiting=!0,this},Context.prototype.switchTo=function(t,e){return"string"==typeof t||t instanceof Pipe?this.nextPipe=t:(this.next=t,e&&(this.nextPipe=e)),this},Context.prototype.push=function(t,e){return t.parent=this,"undefined"!=typeof e&&(t.childName=e),t.root=this.root||this,t.options=t.options||this.options,this.children?(this.children[this.children.length-1].next=t,this.children.push(t)):(this.children=[t],this.nextAfterChildren=this.next||null,this.next=t),t.next=this,this},exports.Context=Context;
},{"../pipe":15}],3:[function(require,module,exports){
var Context=require("./context").Context,dateReviver=require("../date-reviver"),DiffContext=function(t,e){this.left=t,this.right=e,this.pipe="diff"};DiffContext.prototype=new Context,DiffContext.prototype.setResult=function(t){if(this.options.cloneDiffValues){var e="function"==typeof this.options.cloneDiffValues?this.options.cloneDiffValues:function(t){return JSON.parse(JSON.stringify(t),dateReviver)};"object"==typeof t[0]&&(t[0]=e(t[0])),"object"==typeof t[1]&&(t[1]=e(t[1]))}return Context.prototype.setResult.apply(this,arguments)},exports.DiffContext=DiffContext;
},{"../date-reviver":6,"./context":2}],4:[function(require,module,exports){
var Context=require("./context").Context,PatchContext=function(t,e){this.left=t,this.delta=e,this.pipe="patch"};PatchContext.prototype=new Context,exports.PatchContext=PatchContext;
},{"./context":2}],5:[function(require,module,exports){
var Context=require("./context").Context,ReverseContext=function(e){this.delta=e,this.pipe="reverse"};ReverseContext.prototype=new Context,exports.ReverseContext=ReverseContext;
},{"./context":2}],6:[function(require,module,exports){
module.exports=function(d,e){var t;return"string"==typeof e&&(t=/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d*))?(Z|([+\-])(\d{2}):(\d{2}))$/.exec(e))?new Date(Date.UTC(+t[1],+t[2]-1,+t[3],+t[4],+t[5],+t[6],+(t[7]||0))):e};
},{}],7:[function(require,module,exports){
var Processor=require("./processor").Processor,Pipe=require("./pipe").Pipe,DiffContext=require("./contexts/diff").DiffContext,PatchContext=require("./contexts/patch").PatchContext,ReverseContext=require("./contexts/reverse").ReverseContext,trivial=require("./filters/trivial"),nested=require("./filters/nested"),arrays=require("./filters/arrays"),dates=require("./filters/dates"),texts=require("./filters/texts"),DiffPatcher=function(e){this.processor=new Processor(e),this.processor.pipe(new Pipe("diff").append(nested.collectChildrenDiffFilter,trivial.diffFilter,dates.diffFilter,texts.diffFilter,nested.objectsDiffFilter,arrays.diffFilter).shouldHaveResult()),this.processor.pipe(new Pipe("patch").append(nested.collectChildrenPatchFilter,arrays.collectChildrenPatchFilter,trivial.patchFilter,texts.patchFilter,nested.patchFilter,arrays.patchFilter).shouldHaveResult()),this.processor.pipe(new Pipe("reverse").append(nested.collectChildrenReverseFilter,arrays.collectChildrenReverseFilter,trivial.reverseFilter,texts.reverseFilter,nested.reverseFilter,arrays.reverseFilter).shouldHaveResult())};DiffPatcher.prototype.options=function(){return this.processor.options.apply(this.processor,arguments)},DiffPatcher.prototype.diff=function(e,r){return this.processor.process(new DiffContext(e,r))},DiffPatcher.prototype.patch=function(e,r){return this.processor.process(new PatchContext(e,r))},DiffPatcher.prototype.reverse=function(e){return this.processor.process(new ReverseContext(e))},DiffPatcher.prototype.unpatch=function(e,r){return this.patch(e,this.reverse(r))},exports.DiffPatcher=DiffPatcher;
},{"./contexts/diff":3,"./contexts/patch":4,"./contexts/reverse":5,"./filters/arrays":9,"./filters/dates":10,"./filters/nested":12,"./filters/texts":13,"./filters/trivial":14,"./pipe":15,"./processor":16}],8:[function(require,module,exports){
exports.isBrowser="undefined"!=typeof window;
},{}],9:[function(require,module,exports){
function arraysHaveMatchByRef(e,t,r,i){for(var a=0;r>a;a++)for(var n=e[a],l=0;i>l;l++){var s=t[l];if(n===s)return!0}}function matchItems(e,t,r,i,a){var n=e[r],l=t[i];if(n===l)return!0;if("object"!=typeof n||"object"!=typeof l)return!1;var s=a.objectHash;if(!s)return a.matchByPosition&&r===i;var o,f;return"number"==typeof r?(a.hashCache1=a.hashCache1||[],o=a.hashCache1[r],"undefined"==typeof o&&(a.hashCache1[r]=o=s(n,r))):o=s(n),"undefined"==typeof o?!1:("number"==typeof i?(a.hashCache2=a.hashCache2||[],f=a.hashCache2[i],"undefined"==typeof f&&(a.hashCache2[i]=f=s(l,i))):f=s(l),"undefined"==typeof f?!1:o===f)}var DiffContext=require("../contexts/diff").DiffContext,PatchContext=require("../contexts/patch").PatchContext,ReverseContext=require("../contexts/reverse").ReverseContext,lcs=require("./lcs"),ARRAY_MOVE=3,isArray="function"==typeof Array.isArray?Array.isArray:function(e){return e instanceof Array},arrayIndexOf="function"==typeof Array.prototype.indexOf?function(e,t){return e.indexOf(t)}:function(e,t){for(var r=e.length,i=0;r>i;i++)if(e[i]===t)return i;return-1},diffFilter=function(e){if(e.leftIsArray){var t,r,i,a,n={objectHash:e.options&&e.options.objectHash,matchByPosition:e.options&&e.options.matchByPosition},l=0,s=0,o=e.left,f=e.right,c=o.length,h=f.length;for(c>0&&h>0&&!n.objectHash&&"boolean"!=typeof n.matchByPosition&&(n.matchByPosition=!arraysHaveMatchByRef(o,f,c,h));c>l&&h>l&&matchItems(o,f,l,l,n);)t=l,a=new DiffContext(e.left[t],e.right[t]),e.push(a,t),l++;for(;c>s+l&&h>s+l&&matchItems(o,f,c-1-s,h-1-s,n);)r=c-1-s,i=h-1-s,a=new DiffContext(e.left[r],e.right[i]),e.push(a,i),s++;var u;if(l+s===c){if(c===h)return void e.setResult(void 0).exit();for(u=u||{_t:"a"},t=l;h-s>t;t++)u[t]=[f[t]];return void e.setResult(u).exit()}if(l+s===h){for(u=u||{_t:"a"},t=l;c-s>t;t++)u["_"+t]=[o[t],0,0];return void e.setResult(u).exit()}delete n.hashCache1,delete n.hashCache2;var d=o.slice(l,c-s),v=f.slice(l,h-s),p=lcs.get(d,v,matchItems,n),y=[];for(u=u||{_t:"a"},t=l;c-s>t;t++)arrayIndexOf(p.indices1,t-l)<0&&(u["_"+t]=[o[t],0,0],y.push(t));var x=!0;e.options&&e.options.arrays&&e.options.arrays.detectMove===!1&&(x=!1);var m=!1;e.options&&e.options.arrays&&e.options.arrays.includeValueOnMove&&(m=!0);var C=y.length;for(t=l;h-s>t;t++){var R=arrayIndexOf(p.indices2,t-l);if(0>R){var A=!1;if(x&&C>0)for(var _=0;C>_;_++)if(r=y[_],matchItems(d,v,r-l,t-l,n)){u["_"+r].splice(1,2,t,ARRAY_MOVE),m||(u["_"+r][0]=""),i=t,a=new DiffContext(e.left[r],e.right[i]),e.push(a,i),y.splice(_,1),A=!0;break}A||(u[t]=[f[t]])}else r=p.indices1[R]+l,i=p.indices2[R]+l,a=new DiffContext(e.left[r],e.right[i]),e.push(a,i)}e.setResult(u).exit()}};diffFilter.filterName="arrays";var compare={numerically:function(e,t){return e-t},numericallyBy:function(e){return function(t,r){return t[e]-r[e]}}},patchFilter=function(e){if(e.nested&&"a"===e.delta._t){var t,r,i=e.delta,a=e.left,n=[],l=[],s=[];for(t in i)if("_t"!==t)if("_"===t[0]){if(0!==i[t][2]&&i[t][2]!==ARRAY_MOVE)throw new Error("only removal or move can be applied at original array indices, invalid diff type: "+i[t][2]);n.push(parseInt(t.slice(1),10))}else 1===i[t].length?l.push({index:parseInt(t,10),value:i[t][0]}):s.push({index:parseInt(t,10),delta:i[t]});for(n=n.sort(compare.numerically),t=n.length-1;t>=0;t--){r=n[t];var o=i["_"+r],f=a.splice(r,1)[0];o[2]===ARRAY_MOVE&&l.push({index:o[1],value:f})}l=l.sort(compare.numericallyBy("index"));var c=l.length;for(t=0;c>t;t++){var h=l[t];a.splice(h.index,0,h.value)}var u,d=s.length;if(d>0)for(t=0;d>t;t++){var v=s[t];u=new PatchContext(e.left[v.index],v.delta),e.push(u,v.index)}return e.children?void e.exit():void e.setResult(e.left).exit()}};patchFilter.filterName="arrays";var collectChildrenPatchFilter=function(e){if(e&&e.children&&"a"===e.delta._t){for(var t,r=e.children.length,i=0;r>i;i++)t=e.children[i],e.left[t.childName]=t.result;e.setResult(e.left).exit()}};collectChildrenPatchFilter.filterName="arraysCollectChildren";var reverseFilter=function(e){if(!e.nested)return void(e.delta[2]===ARRAY_MOVE&&(e.newName="_"+e.delta[1],e.setResult([e.delta[0],parseInt(e.childName.substr(1),10),ARRAY_MOVE]).exit()));if("a"===e.delta._t){var t,r;for(t in e.delta)"_t"!==t&&(r=new ReverseContext(e.delta[t]),e.push(r,t));e.exit()}};reverseFilter.filterName="arrays";var reverseArrayDeltaIndex=function(e,t,r){if("string"==typeof t&&"_"===t[0])return parseInt(t.substr(1),10);if(isArray(r)&&0===r[2])return"_"+t;var i=+t;for(var a in e){var n=e[a];if(isArray(n))if(n[2]===ARRAY_MOVE){var l=parseInt(a.substr(1),10),s=n[1];if(s===+t)return l;i>=l&&s>i?i++:l>=i&&i>s&&i--}else if(0===n[2]){var o=parseInt(a.substr(1),10);i>=o&&i++}else 1===n.length&&i>=a&&i--}return i},collectChildrenReverseFilter=function(e){if(e&&e.children&&"a"===e.delta._t){for(var t,r=e.children.length,i={_t:"a"},a=0;r>a;a++){t=e.children[a];var n=t.newName;"undefined"==typeof n&&(n=reverseArrayDeltaIndex(e.delta,t.childName,t.result)),i[n]!==t.result&&(i[n]=t.result)}e.setResult(i).exit()}};collectChildrenReverseFilter.filterName="arraysCollectChildren",exports.diffFilter=diffFilter,exports.patchFilter=patchFilter,exports.collectChildrenPatchFilter=collectChildrenPatchFilter,exports.reverseFilter=reverseFilter,exports.collectChildrenReverseFilter=collectChildrenReverseFilter;
},{"../contexts/diff":3,"../contexts/patch":4,"../contexts/reverse":5,"./lcs":11}],10:[function(require,module,exports){
var diffFilter=function(t){t.left instanceof Date?(t.right instanceof Date?t.left.getTime()!==t.right.getTime()?t.setResult([t.left,t.right]):t.setResult(void 0):t.setResult([t.left,t.right]),t.exit()):t.right instanceof Date&&t.setResult([t.left,t.right]).exit()};diffFilter.filterName="dates",exports.diffFilter=diffFilter;
},{}],11:[function(require,module,exports){
var defaultMatch=function(t,e,n,r){return t[n]===e[r]},lengthMatrix=function(t,e,n,r){var c,a,i=t.length,u=e.length,f=[i+1];for(c=0;i+1>c;c++)for(f[c]=[u+1],a=0;u+1>a;a++)f[c][a]=0;for(f.match=n,c=1;i+1>c;c++)for(a=1;u+1>a;a++)n(t,e,c-1,a-1,r)?f[c][a]=f[c-1][a-1]+1:f[c][a]=Math.max(f[c-1][a],f[c][a-1]);return f},backtrack=function(t,e,n,r,c,a){if(0===r||0===c)return{sequence:[],indices1:[],indices2:[]};if(t.match(e,n,r-1,c-1,a)){var i=backtrack(t,e,n,r-1,c-1,a);return i.sequence.push(e[r-1]),i.indices1.push(r-1),i.indices2.push(c-1),i}return t[r][c-1]>t[r-1][c]?backtrack(t,e,n,r,c-1,a):backtrack(t,e,n,r-1,c,a)},get=function(t,e,n,r){r=r||{};var c=lengthMatrix(t,e,n||defaultMatch,r),a=backtrack(c,t,e,t.length,e.length,r);return"string"==typeof t&&"string"==typeof e&&(a.sequence=a.sequence.join("")),a};exports.get=get;
},{}],12:[function(require,module,exports){
var DiffContext=require("../contexts/diff").DiffContext,PatchContext=require("../contexts/patch").PatchContext,ReverseContext=require("../contexts/reverse").ReverseContext,collectChildrenDiffFilter=function(e){if(e&&e.children){for(var t,l=e.children.length,r=e.result,i=0;l>i;i++)t=e.children[i],"undefined"!=typeof t.result&&(r=r||{},r[t.childName]=t.result);r&&e.leftIsArray&&(r._t="a"),e.setResult(r).exit()}};collectChildrenDiffFilter.filterName="collectChildren";var objectsDiffFilter=function(e){if(!e.leftIsArray&&"object"===e.leftType){var t,l,r=e.options.propertyFilter;for(t in e.left)Object.prototype.hasOwnProperty.call(e.left,t)&&(r&&!r(t,e)||(l=new DiffContext(e.left[t],e.right[t]),e.push(l,t)));for(t in e.right)Object.prototype.hasOwnProperty.call(e.right,t)&&(r&&!r(t,e)||"undefined"==typeof e.left[t]&&(l=new DiffContext(void 0,e.right[t]),e.push(l,t)));return e.children&&0!==e.children.length?void e.exit():void e.setResult(void 0).exit()}};objectsDiffFilter.filterName="objects";var patchFilter=function(e){if(e.nested&&!e.delta._t){var t,l;for(t in e.delta)l=new PatchContext(e.left[t],e.delta[t]),e.push(l,t);e.exit()}};patchFilter.filterName="objects";var collectChildrenPatchFilter=function(e){if(e&&e.children&&!e.delta._t){for(var t,l=e.children.length,r=0;l>r;r++)t=e.children[r],Object.prototype.hasOwnProperty.call(e.left,t.childName)&&void 0===t.result?delete e.left[t.childName]:e.left[t.childName]!==t.result&&(e.left[t.childName]=t.result);e.setResult(e.left).exit()}};collectChildrenPatchFilter.filterName="collectChildren";var reverseFilter=function(e){if(e.nested&&!e.delta._t){var t,l;for(t in e.delta)l=new ReverseContext(e.delta[t]),e.push(l,t);e.exit()}};reverseFilter.filterName="objects";var collectChildrenReverseFilter=function(e){if(e&&e.children&&!e.delta._t){for(var t,l=e.children.length,r={},i=0;l>i;i++)t=e.children[i],r[t.childName]!==t.result&&(r[t.childName]=t.result);e.setResult(r).exit()}};collectChildrenReverseFilter.filterName="collectChildren",exports.collectChildrenDiffFilter=collectChildrenDiffFilter,exports.objectsDiffFilter=objectsDiffFilter,exports.patchFilter=patchFilter,exports.collectChildrenPatchFilter=collectChildrenPatchFilter,exports.reverseFilter=reverseFilter,exports.collectChildrenReverseFilter=collectChildrenReverseFilter;
},{"../contexts/diff":3,"../contexts/patch":4,"../contexts/reverse":5}],13:[function(require,module,exports){
var TEXT_DIFF=2,DEFAULT_MIN_LENGTH=60,cachedDiffPatch=null,getDiffMatchPatch=function(t){if(!cachedDiffPatch){var e;if("undefined"!=typeof diff_match_patch)e="function"==typeof diff_match_patch?new diff_match_patch:new diff_match_patch.diff_match_patch;else if("function"==typeof require)try{var i="diff_match_patch_uncompressed",f=require("../../public/external/"+i);e=new f.diff_match_patch}catch(r){e=null}if(!e){if(!t)return null;var a=new Error("text diff_match_patch library not found");throw a.diff_match_patch_not_found=!0,a}cachedDiffPatch={diff:function(t,i){return e.patch_toText(e.patch_make(t,i))},patch:function(t,i){for(var f=e.patch_apply(e.patch_fromText(i),t),r=0;r<f[1].length;r++)if(!f[1][r]){var a=new Error("text patch failed");a.textPatchFailed=!0}return f[0]}}}return cachedDiffPatch},diffFilter=function(t){if("string"===t.leftType){var e=t.options&&t.options.textDiff&&t.options.textDiff.minLength||DEFAULT_MIN_LENGTH;if(t.left.length<e||t.right.length<e)return void t.setResult([t.left,t.right]).exit();var i=getDiffMatchPatch();if(!i)return void t.setResult([t.left,t.right]).exit();var f=i.diff;t.setResult([f(t.left,t.right),0,TEXT_DIFF]).exit()}};diffFilter.filterName="texts";var patchFilter=function(t){if(!t.nested&&t.delta[2]===TEXT_DIFF){var e=getDiffMatchPatch(!0).patch;t.setResult(e(t.left,t.delta[0])).exit()}};patchFilter.filterName="texts";var textDeltaReverse=function(t){var e,i,f,r,a,c,l,n,h=null,d=/^@@ +\-(\d+),(\d+) +\+(\d+),(\d+) +@@$/;for(f=t.split("\n"),e=0,i=f.length;i>e;e++){r=f[e];var o=r.slice(0,1);"@"===o?(h=d.exec(r),c=e,l=null,n=null,f[c]="@@ -"+h[3]+","+h[4]+" +"+h[1]+","+h[2]+" @@"):"+"===o?(l=e,f[e]="-"+f[e].slice(1),"+"===f[e-1].slice(0,1)&&(a=f[e],f[e]=f[e-1],f[e-1]=a)):"-"===o&&(n=e,f[e]="+"+f[e].slice(1))}return f.join("\n")},reverseFilter=function(t){t.nested||t.delta[2]===TEXT_DIFF&&t.setResult([textDeltaReverse(t.delta[0]),0,TEXT_DIFF]).exit()};reverseFilter.filterName="texts",exports.diffFilter=diffFilter,exports.patchFilter=patchFilter,exports.reverseFilter=reverseFilter;
},{}],14:[function(require,module,exports){
var isArray="function"==typeof Array.isArray?Array.isArray:function(e){return e instanceof Array},diffFilter=function(e){if(e.left===e.right)return void e.setResult(void 0).exit();if("undefined"==typeof e.left){if("function"==typeof e.right)throw new Error("functions are not supported");return void e.setResult([e.right]).exit()}if("undefined"==typeof e.right)return void e.setResult([e.left,0,0]).exit();if("function"==typeof e.left||"function"==typeof e.right)throw new Error("functions are not supported");return e.leftType=null===e.left?"null":typeof e.left,e.rightType=null===e.right?"null":typeof e.right,e.leftType!==e.rightType?void e.setResult([e.left,e.right]).exit():"boolean"===e.leftType||"number"===e.leftType?void e.setResult([e.left,e.right]).exit():("object"===e.leftType&&(e.leftIsArray=isArray(e.left)),"object"===e.rightType&&(e.rightIsArray=isArray(e.right)),e.leftIsArray!==e.rightIsArray?void e.setResult([e.left,e.right]).exit():void 0)};diffFilter.filterName="trivial";var patchFilter=function(e){return"undefined"==typeof e.delta?void e.setResult(e.left).exit():(e.nested=!isArray(e.delta),e.nested?void 0:1===e.delta.length?void e.setResult(e.delta[0]).exit():2===e.delta.length?void e.setResult(e.delta[1]).exit():3===e.delta.length&&0===e.delta[2]?void e.setResult(void 0).exit():void 0)};patchFilter.filterName="trivial";var reverseFilter=function(e){return"undefined"==typeof e.delta?void e.setResult(e.delta).exit():(e.nested=!isArray(e.delta),e.nested?void 0:1===e.delta.length?void e.setResult([e.delta[0],0,0]).exit():2===e.delta.length?void e.setResult([e.delta[1],e.delta[0]]).exit():3===e.delta.length&&0===e.delta[2]?void e.setResult([e.delta[0]]).exit():void 0)};reverseFilter.filterName="trivial",exports.diffFilter=diffFilter,exports.patchFilter=patchFilter,exports.reverseFilter=reverseFilter;
},{}],15:[function(require,module,exports){
var Pipe=function(t){this.name=t,this.filters=[]};Pipe.prototype.process=function(t){if(!this.processor)throw new Error("add this pipe to a processor before using it");for(var e=this.debug,r=this.filters.length,i=t,s=0;r>s;s++){var o=this.filters[s];if(e&&this.log("filter: "+o.filterName),o(i),"object"==typeof i&&i.exiting){i.exiting=!1;break}}!i.next&&this.resultCheck&&this.resultCheck(i)},Pipe.prototype.log=function(t){console.log("[jsondiffpatch] "+this.name+" pipe, "+t)},Pipe.prototype.append=function(){return this.filters.push.apply(this.filters,arguments),this},Pipe.prototype.prepend=function(){return this.filters.unshift.apply(this.filters,arguments),this},Pipe.prototype.indexOf=function(t){if(!t)throw new Error("a filter name is required");for(var e=0;e<this.filters.length;e++){var r=this.filters[e];if(r.filterName===t)return e}throw new Error("filter not found: "+t)},Pipe.prototype.list=function(){for(var t=[],e=0;e<this.filters.length;e++){var r=this.filters[e];t.push(r.filterName)}return t},Pipe.prototype.after=function(t){var e=this.indexOf(t),r=Array.prototype.slice.call(arguments,1);if(!r.length)throw new Error("a filter is required");return r.unshift(e+1,0),Array.prototype.splice.apply(this.filters,r),this},Pipe.prototype.before=function(t){var e=this.indexOf(t),r=Array.prototype.slice.call(arguments,1);if(!r.length)throw new Error("a filter is required");return r.unshift(e,0),Array.prototype.splice.apply(this.filters,r),this},Pipe.prototype.clear=function(){return this.filters.length=0,this},Pipe.prototype.shouldHaveResult=function(t){if(t===!1)return void(this.resultCheck=null);if(!this.resultCheck){var e=this;return this.resultCheck=function(t){if(!t.hasResult){console.log(t);var r=new Error(e.name+" failed");throw r.noResult=!0,r}},this}},exports.Pipe=Pipe;
},{}],16:[function(require,module,exports){
var Processor=function(e){this.selfOptions=e||{},this.pipes={}};Processor.prototype.options=function(e){return e&&(this.selfOptions=e),this.selfOptions},Processor.prototype.pipe=function(e,t){if("string"==typeof e){if("undefined"==typeof t)return this.pipes[e];this.pipes[e]=t}if(e&&e.name){if(t=e,t.processor===this)return t;this.pipes[t.name]=t}return t.processor=this,t},Processor.prototype.process=function(e,t){var s=e;s.options=this.options();for(var r,o,i=t||e.pipe||"default";i;)"undefined"!=typeof s.nextAfterChildren&&(s.next=s.nextAfterChildren,s.nextAfterChildren=null),"string"==typeof i&&(i=this.pipe(i)),i.process(s),o=s,r=i,i=null,s&&s.next&&(s=s.next,i=o.nextPipe||s.pipe||r);return s.hasResult?s.result:void 0},exports.Processor=Processor;
},{}]},{},[1])(1)
});


//# sourceMappingURL=jsondiffpatch.min.map