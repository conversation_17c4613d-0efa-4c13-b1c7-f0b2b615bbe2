/* angular */
.ng-hide {
  display: none !important;
}
/* helpers */
.clear-padding {
  padding: 0 !important;
}
/* basic */
a[role="button"] {
  cursor: pointer;
}
.form-control.ng-invalid {
  border-color: #a94442;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.form-control.ng-invalid:hover {
  border-color: #843534;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
}
.disabled .btn {
  background-color: #ddd !important;
  border-color: #ccc !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
  opacity: .65 !important;
  box-shadow: none !important;
}
/* profile */
.virtual-profile-icon {
  border: dotted 1px;
  margin: -1px;
}
.profile-inline {
  background-color: #eee;
  color: #333;
  padding: 0 5px;
}
/* omega-profile-select */
.omega-profile-select {
  width: 100%;
}
.omega-profile-select .dropdown-menu > li > a {
  max-width: none !important;
}
.omega-profile-select .btn {
  width: 100%;
  display: block;
  text-align: left;
  text-align: initial;
  position: relative;
  padding-right: 25px;
}
.omega-profile-select .btn .caret {
  position: absolute;
  top: 50%;
  right: 12px;
  margin-top: -2px;
  vertical-align: middle;
}
.omega-profile-select .dropdown-menu {
  width: 100%;
  cursor: pointer;
}
.monospace {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace !important;
}
.width-initial {
  width: auto !important;
  width: initial !important;
}
.width-limit {
  max-width: 600px;
}
textarea.width-limit {
  max-width: 80%;
}
.width-limit-lg {
  max-width: 800px;
}
.width-limit-xl {
  max-width: 1000px;
}
.inline-form-control {
  display: inline-block;
  margin-right: 20px;
  margin-left: 20px;
  min-width: 50%;
  width: 50% !important;
  width: initial !important;
}
.no-min-width {
  min-width: 0 !important;
}
.align-initial {
  text-align: left;
  text-align: initial;
}
.opacity-half {
  opacity: .5 !important;
}
.opacity-0 {
  opacity: 0 !important;
}
ul.list-style-none,
li.list-style-none {
  list-style: none;
  padding-left: 0;
}
.help-inline {
  margin-left: 10px;
  color: #595959;
}
.settings-group > * {
  margin-left: 30px;
}
.settings-group > h3 {
  margin-left: 0;
}
/* shepherd.js */
.shepherd-element {
  z-index: 20;
}
.shepherd-active:before {
  content: " ";
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10;
}
.shepherd-active .switch-rules {
  position: relative;
  background-color: #7f7f7f;
  z-index: 11;
}
.shepherd-active .switch-rules,
.shepherd-active .switch-rules th,
.shepherd-active .switch-rules td,
.shepherd-active .switch-rules tbody {
  border-color: #6e6e6e !important;
}
.shepherd-active .switch-rules .btn {
  opacity: .5;
}
.shepherd-active .switch-rules .form-control,
.shepherd-active .switch-rules .btn-default {
  background-color: #7f7f7f;
  border-color: #6e6e6e;
  opacity: 1;
}
.shepherd-active .switch-rules .shepherd-enabled {
  background-color: #fff;
  border-color: #ddd !important;
}
.shepherd-active .switch-rules .shepherd-enabled,
.shepherd-active .switch-rules .shepherd-enabled td,
.shepherd-active .switch-rules .shepherd-enabled tbody {
  border-color: #ddd !important;
}
.shepherd-active .switch-rules .shepherd-enabled .btn {
  opacity: 1;
}
.shepherd-active .switch-rules .shepherd-enabled .form-control,
.shepherd-active .switch-rules .shepherd-enabled .btn-default {
  background-color: #fff;
  border-color: #ddd;
}
.shepherd-active .condition-help-section {
  position: relative;
  z-index: 11;
  background-color: #fff;
}
.shepherd-active:not([data-shepherd-step="fixed-servers-step"]) .side-nav {
  position: absolute;
  overflow: visible;
  bottom: 0;
}
.shepherd-active:not([data-shepherd-step="fixed-servers-step"]) .shepherd-enabled > a {
  color: #428bca;
  background-color: #fff;
}
.shepherd-active[data-shepherd-step="switch-default-step"] .sort-bar {
  position: relative;
  z-index: 11;
  background-color: #ffc;
}
.shepherd-enabled {
  position: relative;
  z-index: 11;
}
.shepherd-enabled.fixed-servers {
  background-color: #fff;
}
.fixed-top-right {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  left: auto !important;
  bottom: auto !important;
  -webkit-transform: none !important;
          transform: none !important;
}
/* alert */
.alert-top-wrapper {
  z-index: 100;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  display: inline-block !important;
  transition: -webkit-transform .5s, opacity .5s;
  transition: transform .5s, opacity .5s;
  transition: -webkit-transform cubic-bezier(0.19, 1, 0.22, 1) 0.5s, opacity cubic-bezier(0.19, 1, 0.22, 1) 0.5s;
  transition: transform cubic-bezier(0.19, 1, 0.22, 1) 0.5s, opacity cubic-bezier(0.19, 1, 0.22, 1) 0.5s;
  pointer-events: none;
  text-align: center;
}
.alert-top-wrapper.ng-hide {
  -webkit-transform: translateY(-100%);
          transform: translateY(-100%);
  opacity: 0;
}
.alert-top-wrapper * {
  pointer-events: auto;
  pointer-events: initial;
  text-align: left;
  text-align: initial;
}
.alert-top-wrapper .alert {
  display: inline-block;
  min-width: 400px;
  margin-bottom: 0;
}
@media (max-width: 767px) {
  .alert-top-wrapper .alert {
    min-width: 80%;
  }
}
/* body */
html {
  height: 100%;
}
body {
  min-height: 100%;
}
.modal-backdrop {
  position: fixed;
  bottom: 0;
}
h1 {
  color: #5c6166;
  font-size: 1.7em;
  font-weight: bold;
  margin-bottom: 1.5em;
}
.side-nav {
  position: fixed;
  height: 100%;
  overflow: auto;
}
@media (max-width: 767px) {
  .side-nav {
    position: static;
    height: auto;
    height: initial;
  }
}
.side-nav .nav-pills > li > a {
  padding: 8px 15px;
}
@media (max-height: 767px) {
  .side-nav .nav-pills > li > a {
    padding: 5px 15px;
  }
}
.side-nav .nav-pills > li > a.btn-success:hover {
  color: #fff;
  background-color: #449d44;
  border-color: #398439;
}
main {
  padding-top: 85px;
  padding-bottom: 20px;
}
main .page-header {
  margin: 0;
  padding: 20px 60px 20px 0;
  position: fixed;
  top: 0;
  width: inherit;
  background-color: rgba(255, 255, 255, 0.6);
  background-image: linear-gradient(to bottom, #ffffff, rgba(255, 255, 255, 0.6));
  max-height: 85px;
  z-index: 3;
}
main .page-header h2 {
  margin: 0;
}
@media (max-width: 767px) {
  main {
    padding-top: 0;
  }
  main .page-header {
    position: static;
    max-height: none;
  }
}
main .ng-enter {
  opacity: 0;
  transition: opacity .5s;
}
main .ng-enter-active {
  opacity: 1;
}
#ngProgress-container {
  position: fixed;
  top: 0;
  width: 100%;
}
.divider {
  height: 1px;
  margin: 9px 1px;
  overflow: hidden;
  background-color: #e5e5e5;
}
.nav-header {
  margin-right: -15px !important;
  margin-left: -15px !important;
  display: block;
  padding: 3px 15px;
  font-size: 11px;
  font-weight: bold;
  line-height: 20px;
  color: #999;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  text-transform: uppercase;
}
#restore-local-file {
  position: absolute;
  visibility: hidden;
}
.virtual-profile-icon {
  border: dotted 1px;
  margin: -1px;
}
.profile-actions {
  float: right;
}
.profile-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.profile-color-editor {
  display: inline-block;
  float: left;
  margin-right: 10px;
}
.profile-color-editor input[type="color"] {
  height: 30px;
  width: 30px;
  padding: 0;
  border: none;
  background: none;
}
.profile-color-editor input[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}
.profile-color-editor .sp-replacer {
  padding: 0;
  border: none;
}
.profile-color-editor .sp-preview,
.profile-color-editor .profile-color-editor-fake {
  margin-right: 0;
  height: 30px;
  width: 30px;
  border: solid 1px #222;
}
.profile-color-editor .sp-dd {
  display: none;
}
@media (min-width: 768px) {
  .table-responsive {
    overflow: visible;
  }
}
@media (max-width: 767px) {
  .table-responsive .dropdown-menu {
    position: static !important;
  }
}
.fixed-servers td,
.switch-rules td {
  vertical-align: middle !important;
}
.condition-help dl {
  margin-left: 5px;
  padding-left: 10px;
  border-left: solid 1px #ccc;
}
.condition-help dt {
  font-size: 1.2em;
}
.condition-help dd {
  padding-left: 20px;
}
.close-condition-help {
  float: none !important;
  margin-left: 15px !important;
  opacity: 1 !important;
}
.close-condition-help:hover,
.close-condition-help:active {
  color: #444 !important;
}
.switch-attached > tr > td {
  background-color: #f9f9f9;
}
.shepherd-active .switch-attached > tr > td {
  background-color: transparent;
}
.switch-attached > tr > td:first-child {
  text-align: center;
}
.fixed-show-advanced td {
  background-color: transparent !important;
}
.fixed-show-advanced .btn-link {
  padding: 0;
}
.proxy-actions {
  text-align: center;
  padding: 0 !important;
}
.proxy-auth-toggle {
  padding: 5px 7px !important;
}
.host-levels-details input {
  width: auto !important;
  width: initial !important;
  display: inline-block;
}
.sort-bar {
  width: 1em;
  text-align: center;
  cursor: move;
}
.switch-rule-row td.has-icon > .form-control {
  display: inline-block;
  width: calc(100% - 1.5em);
}
.switch-rule-row td.has-icon > .icon-wrapper {
  display: block;
  float: right;
  line-height: 34px;
}
.cycle-profile-container {
  list-style-type: none;
  min-height: 20px;
  display: inline-block;
  min-width: 40%;
  padding: 0;
  border: solid 1px #abc;
}
.cycle-profile-container li {
  display: block;
  margin: 10px;
  padding: 5px;
  cursor: move;
  background-color: #ddd;
}
.cycle-profile-container.cycle-enabled li {
  background-color: #e4ffcd;
}
.modal-body .well:last-child {
  margin-bottom: 0;
}
