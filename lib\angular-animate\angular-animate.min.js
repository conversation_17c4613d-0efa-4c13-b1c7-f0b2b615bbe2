/*
 AngularJS v1.7.3
 (c) 2010-2018 Google, Inc. http://angularjs.org
 License: MIT
*/
(function(Z,x){'use strict';function Fa(a,b,c){if(!a)throw Pa("areq",b||"?",c||"required");return a}function Ga(a,b){if(!a&&!b)return"";if(!a)return b;if(!b)return a;$(a)&&(a=a.join(" "));$(b)&&(b=b.join(" "));return a+" "+b}function Qa(a){var b={};a&&(a.to||a.from)&&(b.to=a.to,b.from=a.from);return b}function aa(a,b,c){var d="";a=$(a)?a:a&&C(a)&&a.length?a.split(/\s+/):[];r(a,function(a,k){a&&0<a.length&&(d+=0<k?" ":"",d+=c?b+a:a+b)});return d}function Ha(a){if(a instanceof D)switch(a.length){case 0:return a;
case 1:if(1===a[0].nodeType)return a;break;default:return D(va(a))}if(1===a.nodeType)return D(a)}function va(a){if(!a[0])return a;for(var b=0;b<a.length;b++){var c=a[b];if(1===c.nodeType)return c}}function Ra(a,b,c){r(b,function(b){a.addClass(b,c)})}function Sa(a,b,c){r(b,function(b){a.removeClass(b,c)})}function ba(a){return function(b,c){c.addClass&&(Ra(a,b,c.addClass),c.addClass=null);c.removeClass&&(Sa(a,b,c.removeClass),c.removeClass=null)}}function pa(a){a=a||{};if(!a.$$prepared){var b=a.domOperation||
O;a.domOperation=function(){a.$$domOperationFired=!0;b();b=O};a.$$prepared=!0}return a}function ja(a,b){Ia(a,b);Ja(a,b)}function Ia(a,b){b.from&&(a.css(b.from),b.from=null)}function Ja(a,b){b.to&&(a.css(b.to),b.to=null)}function U(a,b,c){var d=b.options||{};c=c.options||{};var g=(d.addClass||"")+" "+(c.addClass||""),k=(d.removeClass||"")+" "+(c.removeClass||"");a=Ta(a.attr("class"),g,k);c.preparationClasses&&(d.preparationClasses=ca(c.preparationClasses,d.preparationClasses),delete c.preparationClasses);
g=d.domOperation!==O?d.domOperation:null;wa(d,c);g&&(d.domOperation=g);d.addClass=a.addClass?a.addClass:null;d.removeClass=a.removeClass?a.removeClass:null;b.addClass=d.addClass;b.removeClass=d.removeClass;return d}function Ta(a,b,c){function d(a){C(a)&&(a=a.split(" "));var c={};r(a,function(a){a.length&&(c[a]=!0)});return c}var g={};a=d(a);b=d(b);r(b,function(a,c){g[c]=1});c=d(c);r(c,function(a,c){g[c]=1===g[c]?null:-1});var k={addClass:"",removeClass:""};r(g,function(c,b){var d,g;1===c?(d="addClass",
g=!a[b]||a[b+"-remove"]):-1===c&&(d="removeClass",g=a[b]||a[b+"-add"]);g&&(k[d].length&&(k[d]+=" "),k[d]+=b)});return k}function J(a){return a instanceof D?a[0]:a}function Ua(a,b,c,d){a="";c&&(a=aa(c,"ng-",!0));d.addClass&&(a=ca(a,aa(d.addClass,"-add")));d.removeClass&&(a=ca(a,aa(d.removeClass,"-remove")));a.length&&(d.preparationClasses=a,b.addClass(a))}function qa(a,b){var c=b?"-"+b+"s":"";ma(a,[na,c]);return[na,c]}function xa(a,b){var c=b?"paused":"",d=da+"PlayState";ma(a,[d,c]);return[d,c]}function ma(a,
b){a.style[b[0]]=b[1]}function ca(a,b){return a?b?a+" "+b:a:b}function Ka(a,b,c){var d=Object.create(null),g=a.getComputedStyle(b)||{};r(c,function(a,c){var b=g[a];if(b){var I=b.charAt(0);if("-"===I||"+"===I||0<=I)b=Va(b);0===b&&(b=null);d[c]=b}});return d}function Va(a){var b=0;a=a.split(/\s*,\s*/);r(a,function(a){"s"===a.charAt(a.length-1)&&(a=a.substring(0,a.length-1));a=parseFloat(a)||0;b=b?Math.max(a,b):a});return b}function ya(a){return 0===a||null!=a}function La(a,b){var c=N,d=a+"s";b?c+="Duration":
d+=" linear all";return[c,d]}function Ma(a,b,c){r(c,function(c){a[c]=za(a[c])?a[c]:b.style.getPropertyValue(c)})}var N,Aa,da,Ba;void 0===Z.ontransitionend&&void 0!==Z.onwebkittransitionend?(N="WebkitTransition",Aa="webkitTransitionEnd transitionend"):(N="transition",Aa="transitionend");void 0===Z.onanimationend&&void 0!==Z.onwebkitanimationend?(da="WebkitAnimation",Ba="webkitAnimationEnd animationend"):(da="animation",Ba="animationend");var ra=da+"Delay",Ca=da+"Duration",na=N+"Delay",Na=N+"Duration",
Pa=x.$$minErr("ng"),Wa={transitionDuration:Na,transitionDelay:na,transitionProperty:N+"Property",animationDuration:Ca,animationDelay:ra,animationIterationCount:da+"IterationCount"},Xa={transitionDuration:Na,transitionDelay:na,animationDuration:Ca,animationDelay:ra},Da,wa,r,$,za,sa,Ea,ta,C,Q,D,O;x.module("ngAnimate",[],function(){O=x.noop;Da=x.copy;wa=x.extend;D=x.element;r=x.forEach;$=x.isArray;C=x.isString;ta=x.isObject;Q=x.isUndefined;za=x.isDefined;Ea=x.isFunction;sa=x.isElement}).info({angularVersion:"1.7.3"}).directive("ngAnimateSwap",
["$animate",function(a){return{restrict:"A",transclude:"element",terminal:!0,priority:600,link:function(b,c,d,g,k){var e,L;b.$watchCollection(d.ngAnimateSwap||d["for"],function(b){e&&a.leave(e);L&&(L.$destroy(),L=null);(b||0===b)&&k(function(b,d){e=b;L=d;a.enter(b,null,c)})})}}}]).directive("ngAnimateChildren",["$interpolate",function(a){return{link:function(b,c,d){function g(a){c.data("$$ngAnimateChildren","on"===a||"true"===a)}var k=d.ngAnimateChildren;C(k)&&0===k.length?c.data("$$ngAnimateChildren",
!0):(g(a(k)(b)),d.$observe("ngAnimateChildren",g))}}}]).factory("$$rAFScheduler",["$$rAF",function(a){function b(a){d=d.concat(a);c()}function c(){if(d.length){for(var b=d.shift(),e=0;e<b.length;e++)b[e]();g||a(function(){g||c()})}}var d,g;d=b.queue=[];b.waitUntilQuiet=function(b){g&&g();g=a(function(){g=null;b();c()})};return b}]).provider("$$animateQueue",["$animateProvider",function(a){function b(a){return{addClass:a.addClass,removeClass:a.removeClass,from:a.from,to:a.to}}function c(a){if(!a)return null;
a=a.split(" ");var b=Object.create(null);r(a,function(a){b[a]=!0});return b}function d(a,b){if(a&&b){var d=c(b);return a.split(" ").some(function(a){return d[a]})}}function g(a,b,c){return e[a].some(function(a){return a(b,c)})}function k(a,b){var c=0<(a.addClass||"").length,d=0<(a.removeClass||"").length;return b?c&&d:c||d}var e=this.rules={skip:[],cancel:[],join:[]};e.join.push(function(a,b){return!a.structural&&k(a)});e.skip.push(function(a,b){return!a.structural&&!k(a)});e.skip.push(function(a,
b){return"leave"===b.event&&a.structural});e.skip.push(function(a,b){return b.structural&&2===b.state&&!a.structural});e.cancel.push(function(a,b){return b.structural&&a.structural});e.cancel.push(function(a,b){return 2===b.state&&a.structural});e.cancel.push(function(a,b){if(b.structural)return!1;var c=a.addClass,g=a.removeClass,k=b.addClass,e=b.removeClass;return Q(c)&&Q(g)||Q(k)&&Q(e)?!1:d(c,e)||d(g,k)});this.$get=["$$rAF","$rootScope","$rootElement","$document","$$Map","$$animation","$$AnimateRunner",
"$templateRequest","$$jqLite","$$forceReflow","$$isDocumentHidden",function(c,d,e,R,V,oa,F,t,s,M,ea){function m(a){ia.delete(a.target)}function w(){var a=!1;return function(b){a?b():d.$$postDigest(function(){a=!0;b()})}}function ua(a,b,c){var f=[],d=E[c];d&&r(d,function(d){Oa.call(d.node,b)?f.push(d.callback):"leave"===c&&Oa.call(d.node,a)&&f.push(d.callback)});return f}function h(a,b,c){var f=va(b);return a.filter(function(a){return!(a.node===f&&(!c||a.callback===c))})}function A(a,H,u){function e(a,
b,f,d){A(function(){var a=ua(E,m,b);a.length?c(function(){r(a,function(a){a(h,f,d)});"close"!==f||m.parentNode||G.off(m)}):"close"!==f||m.parentNode||G.off(m)});a.progress(b,f,d)}function M(a){var b=h,c=n;c.preparationClasses&&(b.removeClass(c.preparationClasses),c.preparationClasses=null);c.activeClasses&&(b.removeClass(c.activeClasses),c.activeClasses=null);X(h,n);ja(h,n);n.domOperation();B.complete(!a)}var n=Da(u),h=Ha(a),m=J(h),E=m&&m.parentNode,n=pa(n),B=new F,A=w();$(n.addClass)&&(n.addClass=
n.addClass.join(" "));n.addClass&&!C(n.addClass)&&(n.addClass=null);$(n.removeClass)&&(n.removeClass=n.removeClass.join(" "));n.removeClass&&!C(n.removeClass)&&(n.removeClass=null);n.from&&!ta(n.from)&&(n.from=null);n.to&&!ta(n.to)&&(n.to=null);if(!(z&&m&&ga(m,H,u)&&Ya(m,n)))return M(),B;var P=0<=["enter","move","leave"].indexOf(H),q=ea(),t=q||ia.get(m);u=!t&&y.get(m)||{};var p=!!u.state;t||p&&1===u.state||(t=!T(m,E,H));if(t)return q&&e(B,H,"start",b(n)),M(),q&&e(B,H,"close",b(n)),B;P&&K(m);q={structural:P,
element:h,event:H,addClass:n.addClass,removeClass:n.removeClass,close:M,options:n,runner:B};if(p){if(g("skip",q,u)){if(2===u.state)return M(),B;U(h,u,q);return u.runner}if(g("cancel",q,u))if(2===u.state)u.runner.end();else if(u.structural)u.close();else return U(h,u,q),u.runner;else if(g("join",q,u))if(2===u.state)U(h,q,{});else return Ua(s,h,P?H:null,n),H=q.event=u.event,n=U(h,u,q),u.runner}else U(h,q,{});(p=q.structural)||(p="animate"===q.event&&0<Object.keys(q.options.to||{}).length||k(q));if(!p)return M(),
f(m),B;var v=(u.counter||0)+1;q.counter=v;l(m,1,q);d.$$postDigest(function(){h=Ha(a);var c=y.get(m),d=!c,c=c||{},s=0<(h.parent()||[]).length&&("animate"===c.event||c.structural||k(c));if(d||c.counter!==v||!s){d&&(X(h,n),ja(h,n));if(d||P&&c.event!==H)n.domOperation(),B.end();s||f(m)}else H=!c.structural&&k(c,!0)?"setClass":c.event,l(m,2),c=oa(h,H,c.options),B.setHost(c),e(B,H,"start",b(n)),c.done(function(a){M(!a);(a=y.get(m))&&a.counter===v&&f(m);e(B,H,"close",b(n))})});return B}function K(a){a=a.querySelectorAll("[data-ng-animate]");
r(a,function(a){var b=parseInt(a.getAttribute("data-ng-animate"),10),c=y.get(a);if(c)switch(b){case 2:c.runner.end();case 1:y.delete(a)}})}function f(a){a.removeAttribute("data-ng-animate");y.delete(a)}function T(a,b,c){c=R[0].body;var f=J(e),d=a===c||"HTML"===a.nodeName,s=a===f,l=!1,m=ia.get(a),h;for((a=D.data(a,"$ngAnimatePin"))&&(b=J(a));b;){s||(s=b===f);if(1!==b.nodeType)break;a=y.get(b)||{};if(!l){var g=ia.get(b);if(!0===g&&!1!==m){m=!0;break}else!1===g&&(m=!1);l=a.structural}if(Q(h)||!0===h)a=
D.data(b,"$$ngAnimateChildren"),za(a)&&(h=a);if(l&&!1===h)break;d||(d=b===c);if(d&&s)break;if(!s&&(a=D.data(b,"$ngAnimatePin"))){b=J(a);continue}b=b.parentNode}return(!l||h)&&!0!==m&&s&&d}function l(a,b,c){c=c||{};c.state=b;a.setAttribute("data-ng-animate",b);c=(b=y.get(a))?wa(b,c):c;y.set(a,c)}var y=new V,ia=new V,z=null,B=d.$watch(function(){return 0===t.totalPendingRequests},function(a){a&&(B(),d.$$postDigest(function(){d.$$postDigest(function(){null===z&&(z=!0)})}))}),E=Object.create(null);V=
a.customFilter();var P=a.classNameFilter();M=function(){return!0};var ga=V||M,Ya=P?function(a,b){var c=[a.getAttribute("class"),b.addClass,b.removeClass].join(" ");return P.test(c)}:M,X=ba(s),Oa=Z.Node.prototype.contains||function(a){return this===a||!!(this.compareDocumentPosition(a)&16)},G={on:function(a,b,c){var f=va(b);E[a]=E[a]||[];E[a].push({node:f,callback:c});D(b).on("$destroy",function(){y.get(f)||G.off(a,b,c)})},off:function(a,b,c){if(1!==arguments.length||C(arguments[0])){var f=E[a];f&&
(E[a]=1===arguments.length?null:h(f,b,c))}else for(f in b=arguments[0],E)E[f]=h(E[f],b)},pin:function(a,b){Fa(sa(a),"element","not an element");Fa(sa(b),"parentElement","not an element");a.data("$ngAnimatePin",b)},push:function(a,b,c,f){c=c||{};c.domOperation=f;return A(a,b,c)},enabled:function(a,b){var c=arguments.length;if(0===c)b=!!z;else if(sa(a)){var f=J(a);if(1===c)b=!ia.get(f);else{if(!ia.has(f))D(a).on("$destroy",m);ia.set(f,!b)}}else b=z=!!a;return b}};return G}]}]).provider("$$animateCache",
function(){var a=0,b=Object.create(null);this.$get=[function(){return{cacheKey:function(b,d,g,k){var e=b.parentNode;b=[e.$$ngAnimateParentKey||(e.$$ngAnimateParentKey=++a),d,b.getAttribute("class")];g&&b.push(g);k&&b.push(k);return b.join(" ")},containsCachedAnimationWithoutDuration:function(a){return(a=b[a])&&!a.isValid||!1},flush:function(){b=Object.create(null)},count:function(a){return(a=b[a])?a.total:0},get:function(a){return(a=b[a])&&a.value},put:function(a,d,g){b[a]?(b[a].total++,b[a].value=
d):b[a]={total:1,value:d,isValid:g}}}}]}).provider("$$animation",["$animateProvider",function(a){var b=this.drivers=[];this.$get=["$$jqLite","$rootScope","$injector","$$AnimateRunner","$$Map","$$rAFScheduler","$$animateCache",function(a,d,g,k,e,L,I){function v(a){function b(a){if(a.processed)return a;a.processed=!0;var d=a.domNode,s=d.parentNode;g.set(d,a);for(var h;s;){if(h=g.get(s)){h.processed||(h=b(h));break}s=s.parentNode}(h||c).children.push(a);return a}var c={children:[]},d,g=new e;for(d=0;d<
a.length;d++){var ea=a[d];g.set(ea.domNode,a[d]={domNode:ea.domNode,element:ea.element,fn:ea.fn,children:[]})}for(d=0;d<a.length;d++)b(a[d]);return function(a){var b=[],c=[],d;for(d=0;d<a.children.length;d++)c.push(a.children[d]);a=c.length;var s=0,g=[];for(d=0;d<c.length;d++){var f=c[d];0>=a&&(a=s,s=0,b.push(g),g=[]);g.push(f);f.children.forEach(function(a){s++;c.push(a)});a--}g.length&&b.push(g);return b}(c)}var R=[],V=ba(a);return function(e,F,t){function s(a){a=a.hasAttribute("ng-animate-ref")?
[a]:a.querySelectorAll("[ng-animate-ref]");var b=[];r(a,function(a){var c=a.getAttribute("ng-animate-ref");c&&c.length&&b.push(a)});return b}function M(a){var b=[],c={};r(a,function(a,d){var f=J(a.element),l=0<=["enter","move"].indexOf(a.event),f=a.structural?s(f):[];if(f.length){var g=l?"to":"from";r(f,function(a){var b=a.getAttribute("ng-animate-ref");c[b]=c[b]||{};c[b][g]={animationID:d,element:D(a)}})}else b.push(a)});var d={},f={};r(c,function(c,s){var l=c.from,g=c.to;if(l&&g){var e=a[l.animationID],
h=a[g.animationID],m=l.animationID.toString();if(!f[m]){var k=f[m]={structural:!0,beforeStart:function(){e.beforeStart();h.beforeStart()},close:function(){e.close();h.close()},classes:ea(e.classes,h.classes),from:e,to:h,anchors:[]};k.classes.length?b.push(k):(b.push(e),b.push(h))}f[m].anchors.push({out:l.element,"in":g.element})}else l=l?l.animationID:g.animationID,g=l.toString(),d[g]||(d[g]=!0,b.push(a[l]))});return b}function ea(a,b){a=a.split(" ");b=b.split(" ");for(var c=[],d=0;d<a.length;d++){var f=
a[d];if("ng-"!==f.substring(0,3))for(var s=0;s<b.length;s++)if(f===b[s]){c.push(f);break}}return c.join(" ")}function m(a){for(var c=b.length-1;0<=c;c--){var d=g.get(b[c])(a);if(d)return d}}function w(a,b){function c(a){(a=a.data("$$animationRunner"))&&a.setHost(b)}a.from&&a.to?(c(a.from.element),c(a.to.element)):c(a.element)}function ua(){var a=e.data("$$animationRunner");!a||"leave"===F&&t.$$domOperationFired||a.end()}function h(b){e.off("$destroy",ua);e.removeData("$$animationRunner");V(e,t);ja(e,
t);t.domOperation();l&&a.removeClass(e,l);f.complete(!b)}var A=J(e);t=pa(t);var K=0<=["enter","move","leave"].indexOf(F),f=new k({end:function(){h()},cancel:function(){h(!0)}});if(!b.length)return h(),f;var T=Ga(e.attr("class"),Ga(t.addClass,t.removeClass)),l=t.tempClasses;l&&(T+=" "+l,t.tempClasses=null);K&&e.data("$$animatePrepareClasses","ng-"+F+"-prepare");e.data("$$animationRunner",f);R.push({element:e,classes:T,event:F,structural:K,options:t,beforeStart:function(){l=(l?l+" ":"")+"ng-animate";
a.addClass(e,l);var b=e.data("$$animatePrepareClasses");b&&a.removeClass(e,b)},close:h});e.on("$destroy",ua);if(1<R.length)return f;d.$$postDigest(function(){var b=[];r(R,function(a){a.element.data("$$animationRunner")?b.push(a):a.close()});R.length=0;var d=M(b),f=[];r(d,function(a){var b=a.from?a.from.element:a.element,c=t.addClass,d=I.cacheKey(A,F,(c?c+" ":"")+"ng-animate",t.removeClass);f.push({element:b,domNode:J(b),fn:function(){var b,c=a.close;if(I.containsCachedAnimationWithoutDuration(d))c();
else{a.beforeStart();if((a.anchors?a.from.element||a.to.element:a.element).data("$$animationRunner")){var f=m(a);f&&(b=f.start)}b?(b=b(),b.done(function(a){c(!a)}),w(a,b)):c()}}})});for(var d=v(f),s=0;s<d.length;s++)for(var l=d[s],g=0;g<l.length;g++){var e=l[g],h=e.element;d[s][g]=e.fn;0===s?h.removeData("$$animatePrepareClasses"):(e=h.data("$$animatePrepareClasses"))&&a.addClass(h,e)}L(d)});return f}}]}]).provider("$animateCss",["$animateProvider",function(a){this.$get=["$window","$$jqLite","$$AnimateRunner",
"$timeout","$$animateCache","$$forceReflow","$sniffer","$$rAFScheduler","$$animateQueue",function(a,c,d,g,k,e,L,I,v){function R(d,g,e,m){var w,v="stagger-"+e;0<k.count(e)&&(w=k.get(v),w||(g=aa(g,"-stagger"),c.addClass(d,g),w=Ka(a,d,m),w.animationDuration=Math.max(w.animationDuration,0),w.transitionDuration=Math.max(w.transitionDuration,0),c.removeClass(d,g),k.put(v,w,!0)));return w||{}}function V(a){t.push(a);I.waitUntilQuiet(function(){k.flush();for(var a=e(),b=0;b<t.length;b++)t[b](a);t.length=
0})}function x(c,d,g,e){d=k.get(g);d||(d=Ka(a,c,Wa),"infinite"===d.animationIterationCount&&(d.animationIterationCount=1));k.put(g,d,e||0<d.transitionDuration||0<d.animationDuration);c=d;g=c.animationDelay;e=c.transitionDelay;c.maxDelay=g&&e?Math.max(g,e):g||e;c.maxDuration=Math.max(c.animationDuration*c.animationIterationCount,c.transitionDuration);return c}var F=ba(c),t=[];return function(a,b){function e(){w()}function m(){w(!0)}function w(b){if(!(B||P&&E)){B=!0;E=!1;W&&!f.$$skipPreparationClasses&&
c.removeClass(a,W);ca&&c.removeClass(a,ca);xa(l,!1);qa(l,!1);r(y,function(a){l.style[a[0]]=""});F(a,f);ja(a,f);Object.keys(T).length&&r(T,function(a,b){a?l.style.setProperty(b,a):l.style.removeProperty(b)});if(f.onDone)f.onDone();u&&u.length&&a.off(u.join(" "),A);var d=a.data("$$animateCss");d&&(g.cancel(d[0].timer),a.removeData("$$animateCss"));ga&&ga.complete(!b)}}function t(a){p.blockTransition&&qa(l,a);p.blockKeyframeAnimation&&xa(l,!!a)}function h(){ga=new d({end:e,cancel:m});V(O);w();return{$$willAnimate:!1,
start:function(){return ga},end:e}}function A(a){a.stopPropagation();var b=a.originalEvent||a;b.target===l&&(a=b.$manualTimeStamp||Date.now(),b=parseFloat(b.elapsedTime.toFixed(3)),Math.max(a-H,0)>=C&&b>=G&&(P=!0,w()))}function K(){function b(){if(!B){t(!1);r(y,function(a){l.style[a[0]]=a[1]});F(a,f);c.addClass(a,ca);if(p.recalculateTimingStyles){U=l.getAttribute("class")+" "+W;la=k.cacheKey(l,ka,f.addClass,f.removeClass);q=x(l,U,la,!1);ha=q.maxDelay;X=Math.max(ha,0);G=q.maxDuration;if(0===G){w();
return}p.hasTransitions=0<q.transitionDuration;p.hasAnimations=0<q.animationDuration}p.applyAnimationDelay&&(ha="boolean"!==typeof f.delay&&ya(f.delay)?parseFloat(f.delay):ha,X=Math.max(ha,0),q.animationDelay=ha,fa=[ra,ha+"s"],y.push(fa),l.style[fa[0]]=fa[1]);C=1E3*X;Q=1E3*G;if(f.easing){var e,h=f.easing;p.hasTransitions&&(e=N+"TimingFunction",y.push([e,h]),l.style[e]=h);p.hasAnimations&&(e=da+"TimingFunction",y.push([e,h]),l.style[e]=h)}q.transitionDuration&&u.push(Aa);q.animationDuration&&u.push(Ba);
H=Date.now();var m=C+1.5*Q;e=H+m;var h=a.data("$$animateCss")||[],n=!0;if(h.length){var K=h[0];(n=e>K.expectedEndTime)?g.cancel(K.timer):h.push(w)}n&&(m=g(d,m,!1),h[0]={timer:m,expectedEndTime:e},h.push(w),a.data("$$animateCss",h));if(u.length)a.on(u.join(" "),A);f.to&&(f.cleanupStyles&&Ma(T,l,Object.keys(f.to)),Ja(a,f))}}function d(){var b=a.data("$$animateCss");if(b){for(var c=1;c<b.length;c++)b[c]();a.removeData("$$animateCss")}}if(!B)if(l.parentNode){var e=function(a){if(P)E&&a&&(E=!1,w());else if(E=
!a,q.animationDuration)if(a=xa(l,E),E)y.push(a);else{var b=y,c=b.indexOf(a);0<=a&&b.splice(c,1)}},h=0<ba&&(q.transitionDuration&&0===Y.transitionDuration||q.animationDuration&&0===Y.animationDuration)&&Math.max(Y.animationDelay,Y.transitionDelay);h?g(b,Math.floor(h*ba*1E3),!1):b();D.resume=function(){e(!0)};D.pause=function(){e(!1)}}else w()}var f=b||{};f.$$prepared||(f=pa(Da(f)));var T={},l=J(a);if(!l||!l.parentNode||!v.enabled())return h();var y=[],I=a.attr("class"),z=Qa(f),B,E,P,ga,D,X,C,G,Q,H,
u=[];if(0===f.duration||!L.animations&&!L.transitions)return h();var ka=f.event&&$(f.event)?f.event.join(" "):f.event,Z=ka&&f.structural,n="",S="";Z?n=aa(ka,"ng-",!0):ka&&(n=ka);f.addClass&&(S+=aa(f.addClass,"-add"));f.removeClass&&(S.length&&(S+=" "),S+=aa(f.removeClass,"-remove"));f.applyClassesEarly&&S.length&&F(a,f);var W=[n,S].join(" ").trim(),U=I+" "+W,I=z.to&&0<Object.keys(z.to).length;if(!(0<(f.keyframeStyle||"").length||I||W))return h();var Y,la=k.cacheKey(l,ka,f.addClass,f.removeClass);
if(k.containsCachedAnimationWithoutDuration(la))return W=null,h();0<f.stagger?(z=parseFloat(f.stagger),Y={transitionDelay:z,animationDelay:z,transitionDuration:0,animationDuration:0}):Y=R(l,W,la,Xa);f.$$skipPreparationClasses||c.addClass(a,W);f.transitionStyle&&(z=[N,f.transitionStyle],ma(l,z),y.push(z));0<=f.duration&&(z=0<l.style[N].length,z=La(f.duration,z),ma(l,z),y.push(z));f.keyframeStyle&&(z=[da,f.keyframeStyle],ma(l,z),y.push(z));var ba=Y?0<=f.staggerIndex?f.staggerIndex:k.count(la):0;(n=
0===ba)&&!f.skipBlocking&&qa(l,9999);var q=x(l,U,la,!Z),ha=q.maxDelay;X=Math.max(ha,0);G=q.maxDuration;var p={};p.hasTransitions=0<q.transitionDuration;p.hasAnimations=0<q.animationDuration;p.hasTransitionAll=p.hasTransitions&&"all"===q.transitionProperty;p.applyTransitionDuration=I&&(p.hasTransitions&&!p.hasTransitionAll||p.hasAnimations&&!p.hasTransitions);p.applyAnimationDuration=f.duration&&p.hasAnimations;p.applyTransitionDelay=ya(f.delay)&&(p.applyTransitionDuration||p.hasTransitions);p.applyAnimationDelay=
ya(f.delay)&&p.hasAnimations;p.recalculateTimingStyles=0<S.length;if(p.applyTransitionDuration||p.applyAnimationDuration)G=f.duration?parseFloat(f.duration):G,p.applyTransitionDuration&&(p.hasTransitions=!0,q.transitionDuration=G,z=0<l.style[N+"Property"].length,y.push(La(G,z))),p.applyAnimationDuration&&(p.hasAnimations=!0,q.animationDuration=G,y.push([Ca,G+"s"]));if(0===G&&!p.recalculateTimingStyles)return h();var ca=aa(W,"-active");if(null!=f.delay){var fa;"boolean"!==typeof f.delay&&(fa=parseFloat(f.delay),
X=Math.max(fa,0));p.applyTransitionDelay&&y.push([na,fa+"s"]);p.applyAnimationDelay&&y.push([ra,fa+"s"])}null==f.duration&&0<q.transitionDuration&&(p.recalculateTimingStyles=p.recalculateTimingStyles||n);C=1E3*X;Q=1E3*G;f.skipBlocking||(p.blockTransition=0<q.transitionDuration,p.blockKeyframeAnimation=0<q.animationDuration&&0<Y.animationDelay&&0===Y.animationDuration);f.from&&(f.cleanupStyles&&Ma(T,l,Object.keys(f.from)),Ia(a,f));p.blockTransition||p.blockKeyframeAnimation?t(G):f.skipBlocking||qa(l,
!1);return{$$willAnimate:!0,end:e,start:function(){if(!B)return D={end:e,cancel:m,resume:null,pause:null},ga=new d(D),V(K),ga}}}}]}]).provider("$$animateCssDriver",["$$animationProvider",function(a){a.drivers.push("$$animateCssDriver");this.$get=["$animateCss","$rootScope","$$AnimateRunner","$rootElement","$sniffer","$$jqLite","$document",function(a,c,d,g,k,e,L){function I(a){return a.replace(/\bng-\S+\b/g,"")}function v(a,b){C(a)&&(a=a.split(" "));C(b)&&(b=b.split(" "));return a.filter(function(a){return-1===
b.indexOf(a)}).join(" ")}function R(c,e,g){function m(a){var b={},c=J(a).getBoundingClientRect();r(["width","height","top","left"],function(a){var d=c[a];switch(a){case "top":d+=F.scrollTop;break;case "left":d+=F.scrollLeft}b[a]=Math.floor(d)+"px"});return b}function k(){var c=I(g.attr("class")||""),d=v(c,A),c=v(A,c),d=a(h,{to:m(g),addClass:"ng-anchor-in "+d,removeClass:"ng-anchor-out "+c,delay:!0});return d.$$willAnimate?d:null}function L(){h.remove();e.removeClass("ng-animate-shim");g.removeClass("ng-animate-shim")}
var h=D(J(e).cloneNode(!0)),A=I(h.attr("class")||"");e.addClass("ng-animate-shim");g.addClass("ng-animate-shim");h.addClass("ng-anchor");t.append(h);var K;c=function(){var c=a(h,{addClass:"ng-anchor-out",delay:!0,from:m(e)});return c.$$willAnimate?c:null}();if(!c&&(K=k(),!K))return L();var f=c||K;return{start:function(){function a(){c&&c.end()}var b,c=f.start();c.done(function(){c=null;if(!K&&(K=k()))return c=K.start(),c.done(function(){c=null;L();b.complete()}),c;L();b.complete()});return b=new d({end:a,
cancel:a})}}}function x(a,b,c,e){var g=oa(a,O),k=oa(b,O),h=[];r(e,function(a){(a=R(c,a.out,a["in"]))&&h.push(a)});if(g||k||0!==h.length)return{start:function(){function a(){r(b,function(a){a.end()})}var b=[];g&&b.push(g.start());k&&b.push(k.start());r(h,function(a){b.push(a.start())});var c=new d({end:a,cancel:a});d.all(b,function(a){c.complete(a)});return c}}}function oa(c){var d=c.element,e=c.options||{};c.structural&&(e.event=c.event,e.structural=!0,e.applyClassesEarly=!0,"leave"===c.event&&(e.onDone=
e.domOperation));e.preparationClasses&&(e.event=ca(e.event,e.preparationClasses));c=a(d,e);return c.$$willAnimate?c:null}if(!k.animations&&!k.transitions)return O;var F=L[0].body;c=J(g);var t=D(c.parentNode&&11===c.parentNode.nodeType||F.contains(c)?c:F);return function(a){return a.from&&a.to?x(a.from,a.to,a.classes,a.anchors):oa(a)}}]}]).provider("$$animateJs",["$animateProvider",function(a){this.$get=["$injector","$$AnimateRunner","$$jqLite",function(b,c,d){function g(c){c=$(c)?c:c.split(" ");for(var d=
[],g={},k=0;k<c.length;k++){var r=c[k],x=a.$$registeredAnimations[r];x&&!g[r]&&(d.push(b.get(x)),g[r]=!0)}return d}var k=ba(d);return function(a,b,d,v){function x(){v.domOperation();k(a,v)}function D(a,b,d,g,e){switch(d){case "animate":b=[b,g.from,g.to,e];break;case "setClass":b=[b,s,M,e];break;case "addClass":b=[b,s,e];break;case "removeClass":b=[b,M,e];break;default:b=[b,e]}b.push(g);if(a=a.apply(a,b))if(Ea(a.start)&&(a=a.start()),a instanceof c)a.done(e);else if(Ea(a))return a;return O}function C(a,
b,d,e,g){var h=[];r(e,function(e){var l=e[g];l&&h.push(function(){var e,g,h=!1,k=function(a){h||(h=!0,(g||O)(a),e.complete(!a))};e=new c({end:function(){k()},cancel:function(){k(!0)}});g=D(l,a,b,d,function(a){k(!1===a)});return e})});return h}function F(a,b,d,e,g){var h=C(a,b,d,e,g);if(0===h.length){var k,m;"beforeSetClass"===g?(k=C(a,"removeClass",d,e,"beforeRemoveClass"),m=C(a,"addClass",d,e,"beforeAddClass")):"setClass"===g&&(k=C(a,"removeClass",d,e,"removeClass"),m=C(a,"addClass",d,e,"addClass"));
k&&(h=h.concat(k));m&&(h=h.concat(m))}if(0!==h.length)return function(a){var b=[];h.length&&r(h,function(a){b.push(a())});b.length?c.all(b,a):a();return function(a){r(b,function(b){a?b.cancel():b.end()})}}}var t=!1;3===arguments.length&&ta(d)&&(v=d,d=null);v=pa(v);d||(d=a.attr("class")||"",v.addClass&&(d+=" "+v.addClass),v.removeClass&&(d+=" "+v.removeClass));var s=v.addClass,M=v.removeClass,J=g(d),m,w;if(J.length){var N,h;"leave"===b?(h="leave",N="afterLeave"):(h="before"+b.charAt(0).toUpperCase()+
b.substr(1),N=b);"enter"!==b&&"move"!==b&&(m=F(a,b,v,J,h));w=F(a,b,v,J,N)}if(m||w){var A;return{$$willAnimate:!0,end:function(){A?A.end():(t=!0,x(),ja(a,v),A=new c,A.complete(!0));return A},start:function(){function b(c){t=!0;x();ja(a,v);A.complete(c)}if(A)return A;A=new c;var d,g=[];m&&g.push(function(a){d=m(a)});g.length?g.push(function(a){x();a(!0)}):x();w&&g.push(function(a){d=w(a)});A.setHost({end:function(){t||((d||O)(void 0),b(void 0))},cancel:function(){t||((d||O)(!0),b(!0))}});c.chain(g,
b);return A}}}}}]}]).provider("$$animateJsDriver",["$$animationProvider",function(a){a.drivers.push("$$animateJsDriver");this.$get=["$$animateJs","$$AnimateRunner",function(a,c){function d(c){return a(c.element,c.event,c.classes,c.options)}return function(a){if(a.from&&a.to){var b=d(a.from),e=d(a.to);if(b||e)return{start:function(){function a(){return function(){r(d,function(a){a.end()})}}var d=[];b&&d.push(b.start());e&&d.push(e.start());c.all(d,function(a){g.complete(a)});var g=new c({end:a(),cancel:a()});
return g}}}else return d(a)}}]}])})(window,window.angular);
//# sourceMappingURL=angular-animate.min.js.map
